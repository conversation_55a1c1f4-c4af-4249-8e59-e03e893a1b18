<template>
  <ant-modal
    :visible="open"
    :modal-title="modalTitle"
    :loading="modalLoading"
    modalWidth="1200"
    @cancel="cancel"
    modalHeight="800"
    :maskClosable="false"
  >
    <div slot="content" style="height: 100%; display: flex; flex-direction: column; position: relative;">
      <a-steps :current="step" size="small" style="padding: 4px 240px">
        <a-step title="基本信息" />
        <a-step title="降雨过程" />
        <a-step title="预报结果" />
      </a-steps>

      <!-- 加载遮罩层 -->
      <div v-if="generatingLoading" class="generating-overlay">
        <div class="generating-content">
          <a-spin size="large" />
          <div class="generating-text">方案生成中...</div>
        </div>
      </div>

      <keep-alive>
        <Basic v-if="step == 0" ref="basicRef" v-bind="$attrs" @saveData="saveData" />
        <!-- <RainfallList v-if="step == 1" ref="rainfallListRef" :baseInfo="baseInfo" @saveData="saveData" /> -->
        <TestList
          v-if="step == 1"
          ref="rainfallListRef"
          :baseInfo="baseInfo"
          :rainfall="rainfall"
          @saveData="saveData"
          @setLoading="handleSetLoading"
        />
      </keep-alive>
      <Result
        v-if="step == 2"
        ref="resultRef"
        :baseInfo="baseInfo"
        :rainfall="rainfall"
        :inWaterId="inWaterId"
        :isDisabledBtn.sync="isDisabledBtn"
        @saveData="saveData"
      />
    </div>
    <template slot="footer">
      <a-button v-if="step === 2" :disabled="isDisabledBtn" @click="onGenerate">生成水库调度</a-button>
      <a-button @click="cancel" v-if="step === 0">取消</a-button>
      <a-button @click="preStep" v-if="step !== 0" :disabled="isDisabledBtn">上一步</a-button>
      <a-button type="primary" @click.stop="onSubmit" :loading="loading" :disabled="isDisabledBtn">
        {{ step === 2 ? '保存' : '下一步' }}
      </a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { getOptions } from '@/api/common'
  import { setInWaterRange, saveInWater } from '../../../services'
  import AntModal from '@/components/pt/dialog/AntModal'
  import Basic from './Basic.vue'
  import RainfallList from './RainfallList.vue'
  import TestList from './TestList.vue'
  import Result from './Result.vue'

  export default {
    name: 'AddModal',
    components: { AntModal, Basic, RainfallList, TestList, Result },
    data() {
      return {
        loading: false,
        modalLoading: false,
        open: false,
        modalTitle: '新增',
        step: 0,
        baseInfo: {},
        rainfall: null,
        inWaterId: null, // 存储预报生成的inWaterId

        isGenerate: false,
        generatingLoading: false, // 方案生成加载状态

        isDisabledBtn: false,
      }
    },
    created() {},
    computed: {},
    watch: {},
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      preStep() {
        this.step--
      },
      handleShow() {
        this.open = true
      },

      // 处理加载状态
      handleSetLoading(loading) {
        this.generatingLoading = loading
        this.isDisabledBtn = loading
      },

      saveData(params) {
        if (!!params) {
          switch (this.step) {
            case 0:
              // 从Basic页面获取数据，包含降雨数据
              console.log('Basic页面返回的数据:', params)

              // 提取基本信息（不包含降雨数据）
              const { rainfallData, measuredRainfallData, ...baseInfo } = params
              this.baseInfo = { ...baseInfo }

              // 设置降雨数据
              this.rainfall = {
                rainfallData: rainfallData || [],
                measuredRainfallData: measuredRainfallData || []
              }

              console.log('Updated baseInfo:', this.baseInfo)
              console.log('Updated rainfall:', this.rainfall)

              this.$nextTick(() => (this.step += 1))
              break
            case 1:
              // 从TestList页面获取数据，包含inWaterId
              if (params && params.inWaterId) {
                this.inWaterId = params.inWaterId
                console.log('获取到inWaterId:', this.inWaterId)
              }
              this.rainfall = params
              this.generatingLoading = false
              this.isDisabledBtn = false
              this.$nextTick(() => (this.step += 1))
              break
            case 2:
              saveInWater({ inWaterId: params.inWaterId }).then(res => {
                this.$message.success('保存成功', 3)
                if (this.isGenerate) {
                  // 打开调度模型的弹框
                  this.$emit('handleAddDispatchDetail', params)
                  this.isGenerate = false
                }
                this.$nextTick(() => {
                  this.$emit('close')
                  this.$emit('ok')
                })
              })
              break
            default:
              break
          }
        }
      },
      onSubmit() {
        switch (this.step) {
          case 0:
            this.$refs.basicRef.save()
            break
          case 1:
            this.$refs.rainfallListRef.save()
            break
          case 2:
            this.$refs.resultRef.save()
            break
          default:
            break
        }
        return
      },
      onGenerate() {
        this.isGenerate = true
        this.$refs.resultRef.save()
      },
    },
  }
</script>

<style lang="less" scoped>
  ::v-deep .modal-content {
    height: 100%;
  }

  .generating-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }

  .generating-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
  }

  .generating-text {
    font-size: 16px;
    color: #1890ff;
    font-weight: 500;
  }
</style>
